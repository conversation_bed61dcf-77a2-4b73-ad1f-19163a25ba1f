package gospeedmeter

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
)

// Unit represents time units for speed calculation
type Unit string

const (
	UnitMS Unit = "ms"
	UnitS  Unit = "s"
	UnitM  Unit = "m"
	UnitH  Unit = "h"
	UnitD  Unit = "d"
)

var unitMap = map[Unit]int64{
	UnitMS: 1,
	UnitS:  1000,
	UnitM:  60000,
	UnitH:  3600000,
	UnitD:  3600000 * 24,
}

const AMOUNT_UNIT = "KMBT"

// SpeedMeter handles speed calculations for multiple meters
type SpeedMeter struct {
	values               map[string]float64
	mu                   sync.Mutex
	startTime            time.Time
	intervalCallback     func(*SpeedMeter)
	intervalTriggerCount int
	counter              int
	expirationDuration   time.Duration
	lastExpirationCheck  time.Time // 记录上次过期检查的时间，避免重复检查
}

// SpeedMeterOptions defines the configuration options for SpeedMeter
type SpeedMeterOptions struct {
	Values               map[string]float64
	IntervalCallback     func(*SpeedMeter)
	IntervalTriggerCount int
	ExpirationHours      int // Expiration time in hours, 0 means no expiration
}

// NewSpeedMeter creates a new SpeedMeter instance
func NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter {
	values := make(map[string]float64)
	intervalCallback := func(sm *SpeedMeter) {
		golog.Info(sm.ToString(UnitM, nil))
	}
	intervalTriggerCount := 1000
	var expirationDuration time.Duration

	if options.Values != nil {
		values = options.Values
	}
	if options.IntervalCallback != nil {
		intervalCallback = options.IntervalCallback
	}
	if options.IntervalTriggerCount > 0 {
		intervalTriggerCount = options.IntervalTriggerCount
	}
	if options.ExpirationHours > 0 {
		expirationDuration = time.Duration(options.ExpirationHours) * time.Hour
	}

	now := time.Now()
	return &SpeedMeter{
		values:               values,
		startTime:            now,
		intervalCallback:     intervalCallback,
		intervalTriggerCount: intervalTriggerCount,
		expirationDuration:   expirationDuration,
		lastExpirationCheck:  now,
	}
}

// Reset resets the speed meter with optional initial values
func (sm *SpeedMeter) Reset(values ...map[string]float64) {
	if len(values) > 0 {
		sm.values = values[0]
	} else {
		sm.values = make(map[string]float64)
	}
	now := time.Now()
	sm.startTime = now
	sm.lastExpirationCheck = now
}

// Check updates a single meter value and triggers callback if needed
func (sm *SpeedMeter) Check(name string, value float64) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Check if expired before updating values
	now := time.Now()
	if sm.expirationDuration > 0 && now.Sub(sm.startTime) >= sm.expirationDuration {
		// Double-check to prevent multiple goroutines from processing the same expiration
		// If another goroutine already processed expiration, startTime would be recent
		if now.Sub(sm.startTime) >= sm.expirationDuration {
			// Get current state for logging using the same format as ToString
			currentState := sm.toStringUnsafe(UnitM, nil)

			// Log expiration with formatted output
			golog.Info(fmt.Sprintf("SpeedMeter expired after %v hours, current state: %s",
				sm.expirationDuration.Hours(), currentState))

			// Reset the meter
			sm.values = make(map[string]float64)
			sm.startTime = now
			sm.counter = 0
			sm.lastExpirationCheck = now
		}
	}

	sm.values[name] += value
	sm.counter++
	counter := sm.counter

	if sm.intervalCallback != nil && counter%sm.intervalTriggerCount == 0 {
		// Release lock before calling callback to avoid potential deadlocks
		sm.mu.Unlock()
		sm.intervalCallback(sm)
		sm.mu.Lock()
	}
}

// getSpeedUnsafe returns the current speed for all meters without locking (internal use only)
func (sm *SpeedMeter) getSpeedUnsafe(unit Unit) map[string]float64 {
	denominator, ok := unitMap[unit]
	if !ok {
		denominator = unitMap[UnitS] // default to seconds
	}
	speed := make(map[string]float64)
	ms := time.Since(sm.startTime).Milliseconds()
	if ms == 0 {
		ms = 1 // prevent divide by 0
	}
	for k, v := range sm.values {
		speed[k] = float64(v) * float64(denominator) / float64(ms)
	}
	return speed
}

// GetSpeed returns the current speed for all meters in the specified unit
func (sm *SpeedMeter) GetSpeed(unit Unit) map[string]float64 {
	sm.mu.Lock()
	speed := sm.getSpeedUnsafe(unit)
	sm.mu.Unlock()
	return speed
}

// toStringUnsafe returns a formatted string representation without locking (internal use only)
func (sm *SpeedMeter) toStringUnsafe(unit Unit, toBeEstimated map[string]float64) string {
	speed := sm.getSpeedUnsafe(unit)

	// 收集所有 meter 名称并按名称升序排序
	var names []string
	for name := range speed {
		names = append(names, name)
	}
	sort.Strings(names)

	var result []string
	for _, name := range names {
		v := speed[name]
		vStr := numberToShow(v)

		// 格式化计数器值，避免科学计数法显示
		counterValue := sm.values[name]
		var counterStr string
		if strings.Contains(name, "Speed") || strings.Contains(name, "Rate") {
			// 对于速度类型，保留2位小数
			counterStr = fmt.Sprintf("%.2f", counterValue)
		} else {
			// 对于计数类型，使用整数格式化
			counterStr = fmt.Sprintf("%.0f", counterValue)
		}

		toShow := fmt.Sprintf("%s(%s):%s/%s", name, counterStr, vStr, unit)
		if toBeEstimated != nil {
			if target, ok := toBeEstimated[name]; ok {
				remaining := math.Max(target-sm.values[name], 0)
				if v > 0 {
					estimate := remaining / v
					toShow += fmt.Sprintf(" est:%s %s", numberToShow(estimate), unit)
				}
			}
		}
		result = append(result, toShow)
	}
	return fmt.Sprintf("%v", result)
}

// ToString returns a formatted string representation of the speed meter
// Results are sorted by name in ascending order
func (sm *SpeedMeter) ToString(unit Unit, toBeEstimated map[string]float64) string {
	sm.mu.Lock()
	result := sm.toStringUnsafe(unit, toBeEstimated)
	sm.mu.Unlock()
	return result
}

// Estimate calculates estimated time to reach target value for a single meter
func (sm *SpeedMeter) Estimate(name string, targetValue float64, unit Unit) float64 {
	speeds := sm.GetSpeed(unit)
	remaining := math.Max(targetValue-sm.values[name], 0)
	if speeds[name] > 0 {
		return remaining / speeds[name]
	}
	return 0
}

// GetCounters returns current counter values
func (sm *SpeedMeter) GetCounters() map[string]float64 {
	sm.mu.Lock()
	result := make(map[string]float64)
	for k, v := range sm.values {
		result[k] = v
	}
	sm.mu.Unlock()
	return result
}

// numberToShow formats a number with K/M/B/T units
func numberToShow(v float64) string {
	amountUnit := ""
	amountUnitAt := 0
	for v > 1000 && amountUnitAt < len(AMOUNT_UNIT) {
		v /= 1000
		amountUnit = string(AMOUNT_UNIT[amountUnitAt])
		amountUnitAt++
	}

	// Format the number with 4 decimal places and limit to 5 characters
	strV := fmt.Sprintf("%.4f", v)
	if len(strV) > 5 {
		strV = strV[:5]
	}
	return strV + amountUnit
}
